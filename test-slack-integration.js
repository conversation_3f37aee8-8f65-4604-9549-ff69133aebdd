#!/usr/bin/env node

/**
 * Test script for Slack Integration
 * Tests the bot token provided by the user and verifies the integration flow
 */

const axios = require("axios");

const BASE_URL = "http://localhost:500";
const BOT_TOKEN = "*********************************************************";

// Test user credentials (you may need to adjust these)
const TEST_USER = {
  email: "<EMAIL>", // Adjust this to match an admin user in your system
};

let authToken = null;

/**
 * Get JWT token for testing
 */
async function getAuthToken() {
  try {
    console.log("🔐 Getting JWT token...");
    const response = await axios.post(`${BASE_URL}/jwt`, {
      email: TEST_USER.email,
    });

    if (response.data.token) {
      authToken = response.data.token;
      console.log("✅ JWT token obtained successfully");
      return true;
    } else {
      console.log("❌ Failed to get JWT token:", response.data);
      return false;
    }
  } catch (error) {
    console.log(
      "❌ Error getting JWT token:",
      error.response?.data || error.message
    );
    return false;
  }
}

/**
 * Test Slack connection
 */
async function testSlackConnection() {
  try {
    console.log("\n🧪 Testing Slack connection...");
    const response = await axios.post(
      `${BASE_URL}/api/integrations/slack/test-connection`,
      { botToken: BOT_TOKEN },
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data.success) {
      console.log("✅ Slack connection test successful!");
      console.log("📊 Connection details:");
      console.log(`   - Team Name: ${response.data.data.teamName}`);
      console.log(`   - Team ID: ${response.data.data.teamId}`);
      console.log(`   - Bot Name: ${response.data.data.botName}`);
      console.log(`   - Bot User ID: ${response.data.data.botUserId}`);
      return response.data.data;
    } else {
      console.log("❌ Slack connection test failed:", response.data.error);
      return null;
    }
  } catch (error) {
    console.log(
      "❌ Error testing Slack connection:",
      error.response?.data || error.message
    );
    return null;
  }
}

/**
 * Connect Slack integration (save to database)
 */
async function connectSlackIntegration() {
  try {
    console.log("\n💾 Connecting Slack integration (saving to database)...");
    const response = await axios.post(
      `${BASE_URL}/api/integrations/slack/connect`,
      { botToken: BOT_TOKEN },
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data.success) {
      console.log("✅ Slack integration connected and saved to database!");
      console.log("📊 Integration details:");
      console.log(`   - Integration ID: ${response.data.data.integrationId}`);
      console.log(`   - Team Name: ${response.data.data.teamName}`);
      console.log(`   - Connected At: ${response.data.data.connectedAt}`);
      console.log(`   - Is Update: ${response.data.data.isUpdate}`);
      return response.data.data;
    } else {
      console.log(
        "❌ Slack integration connection failed:",
        response.data.error
      );
      return null;
    }
  } catch (error) {
    console.log(
      "❌ Error connecting Slack integration:",
      error.response?.data || error.message
    );
    return null;
  }
}

/**
 * Get integration status
 */
async function getIntegrationStatus() {
  try {
    console.log("\n📋 Getting integration status...");
    const response = await axios.get(
      `${BASE_URL}/api/integrations/slack/status`,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      }
    );

    if (response.data.success) {
      console.log("✅ Integration status retrieved!");
      console.log("📊 Status details:");
      console.log(`   - Is Connected: ${response.data.data.isConnected}`);
      console.log(`   - Team Name: ${response.data.data.teamName || "N/A"}`);
      console.log(
        `   - Last Test: ${response.data.data.lastTestAt || "Never"}`
      );
      return response.data.data;
    } else {
      console.log("❌ Failed to get integration status:", response.data.error);
      return null;
    }
  } catch (error) {
    console.log(
      "❌ Error getting integration status:",
      error.response?.data || error.message
    );
    return null;
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log("🚀 Starting Slack Integration Tests");
  console.log("=====================================");

  // Step 1: Get authentication token
  const authSuccess = await getAuthToken();
  if (!authSuccess) {
    console.log("\n❌ Cannot proceed without authentication token");
    process.exit(1);
  }

  // Step 2: Test Slack connection
  const testResult = await testSlackConnection();
  if (!testResult) {
    console.log("\n❌ Slack connection test failed - cannot proceed");
    process.exit(1);
  }

  // Step 3: Connect and save to database
  const connectResult = await connectSlackIntegration();
  if (!connectResult) {
    console.log("\n❌ Failed to save integration to database");
    process.exit(1);
  }

  // Step 4: Verify integration status
  const statusResult = await getIntegrationStatus();

  console.log("\n🎉 All tests completed successfully!");
  console.log("=====================================");
  console.log("✅ Bot token is valid");
  console.log("✅ Connection test passed");
  console.log("✅ Integration saved to database");
  console.log("✅ Status verification completed");

  if (statusResult && statusResult.isConnected) {
    console.log("\n🔗 Your Slack integration is now active and ready to use!");
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch((error) => {
    console.error("💥 Test execution failed:", error);
    process.exit(1);
  });
}

module.exports = {
  getAuthToken,
  testSlackConnection,
  connectSlackIntegration,
  getIntegrationStatus,
};
