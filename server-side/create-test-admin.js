#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create a test admin user for Slack integration testing
 */

const { MongoClient } = require('mongodb');

const MONGODB_URI = 'mongodb://127.0.0.1:27017';
const DB_NAME = 'wpdevDB';

const TEST_ADMIN = {
  name: 'Test Admin',
  email: '<EMAIL>',
  role: 'admin',
  image: 'https://www.gravatar.com/avatar/test?d=identicon'
};

async function createTestAdmin() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    console.log('🔌 Connecting to MongoDB...');
    await client.connect();
    
    const db = client.db(DB_NAME);
    const userCollection = db.collection('users');
    
    // Check if user already exists
    const existingUser = await userCollection.findOne({ email: TEST_ADMIN.email });
    
    if (existingUser) {
      console.log('✅ Test admin user already exists');
      console.log('📊 User details:');
      console.log(`   - Name: ${existingUser.name}`);
      console.log(`   - Email: ${existingUser.email}`);
      console.log(`   - Role: ${existingUser.role}`);
      return existingUser;
    }
    
    // Create the test admin user
    console.log('👤 Creating test admin user...');
    const result = await userCollection.insertOne(TEST_ADMIN);
    
    if (result.insertedId) {
      console.log('✅ Test admin user created successfully!');
      console.log('📊 User details:');
      console.log(`   - ID: ${result.insertedId}`);
      console.log(`   - Name: ${TEST_ADMIN.name}`);
      console.log(`   - Email: ${TEST_ADMIN.email}`);
      console.log(`   - Role: ${TEST_ADMIN.role}`);
      return { ...TEST_ADMIN, _id: result.insertedId };
    } else {
      throw new Error('Failed to create user');
    }
    
  } catch (error) {
    console.error('❌ Error creating test admin user:', error);
    throw error;
  } finally {
    await client.close();
    console.log('🔌 MongoDB connection closed');
  }
}

// Run the script
if (require.main === module) {
  createTestAdmin()
    .then(() => {
      console.log('\n🎉 Test admin user setup completed!');
      console.log('You can now use this user for Slack integration testing.');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Script execution failed:', error);
      process.exit(1);
    });
}

module.exports = { createTestAdmin, TEST_ADMIN };
