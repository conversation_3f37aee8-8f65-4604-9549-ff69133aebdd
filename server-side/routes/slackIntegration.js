const express = require("express");
const { ObjectId } = require("mongodb");
const SlackIntegrationService = require("../services/slackIntegrationService");
const { verifyToken, verifyAdmin } = require("../middleware/auth");

const router = express.Router();

/**
 * SLACK INTEGRATION API ENDPOINTS
 *
 * Expected Frontend Contracts:
 * - POST /connect: { botToken: "xoxb-..." } → { success: true, data: {...}, message: "..." }
 * - POST /test-connection: { botToken: "xoxb-..." } → { success: true, data: {...}, message: "..." }
 * - PATCH /toggle: { enabled: boolean } → { success: true, data: {...}, message: "..." }
 *
 * Error Response Format: { success: false, error: "human-readable message", details?: {...} }
 *
 * Authentication: All endpoints require valid JWT token + admin role
 *
 * Manual Testing:
 * curl -X POST http://localhost:500/api/integrations/slack/connect \
 *   -H "Content-Type: application/json" \
 *   -H "Authorization: Bearer YOUR_JWT_TOKEN" \
 *   -d '{"botToken":"xoxb-your-token"}'
 */

// Utility function to sanitize payloads for logging (removes sensitive data)
const sanitizeForLogging = (payload) => {
  if (!payload) return payload;
  const sanitized = { ...payload };
  if (sanitized.botToken) {
    sanitized.botToken = `${sanitized.botToken.substring(0, 10)}...`;
  }
  return sanitized;
};

/**
 * Initialize Slack integration routes
 * @param {Object} db - MongoDB database instance
 * @returns {Router} Express router
 */
function createSlackIntegrationRoutes(db) {
  const slackService = new SlackIntegrationService(db);

  /**
   * GET /api/integrations/slack/status
   * Get current Slack integration status
   */
  router.get("/status", verifyToken, verifyAdmin(db), async (req, res) => {
    try {
      const status = await slackService.getIntegrationStatus();
      res.json({
        success: true,
        data: status,
      });
    } catch (error) {
      console.error("Error getting Slack integration status:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get integration status",
      });
    }
  });

  /**
   * POST /api/integrations/slack/connect
   * Connect or update Slack integration
   *
   * Expected payload: { botToken: "xoxb-..." }
   * Success response: { success: true, data: { teamName, teamId, connectedAt, integrationId, isUpdate }, message: "..." }
   * Error response: { success: false, error: "...", details?: {...} }
   */
  router.post("/connect", verifyToken, verifyAdmin(db), async (req, res) => {
    console.log(
      `[SLACK-CONNECT] Request from user ${req.user?.email}, payload:`,
      sanitizeForLogging(req.body)
    );

    try {
      const { botToken } = req.body;

      // Validate required fields
      if (!botToken || typeof botToken !== "string") {
        console.log(
          `[SLACK-CONNECT] Validation failed: missing or invalid botToken`
        );
        return res.status(400).json({
          success: false,
          error: "Bot token is required and must be a string",
          details: { field: "botToken", received: typeof botToken },
        });
      }

      // Validate bot token format
      if (!botToken.startsWith("xoxb-")) {
        console.log(`[SLACK-CONNECT] Validation failed: invalid token format`);
        return res.status(400).json({
          success: false,
          error:
            'Invalid bot token format. Bot tokens should start with "xoxb-"',
          details: { field: "botToken", format: "Expected format: xoxb-..." },
        });
      }

      // Call service method
      const result = await slackService.createOrUpdateIntegration(
        botToken,
        req.user.userId
      );

      if (!result.success) {
        console.log(`[SLACK-CONNECT] Service failed:`, result.error);
        return res.status(400).json({
          success: false,
          error: result.error,
          details: result.details || {},
        });
      }

      console.log(
        `[SLACK-CONNECT] Success: ${
          result.isUpdate ? "Updated" : "Created"
        } integration for team ${result.integration?.teamName}`
      );

      res.json({
        success: true,
        data: {
          integrationId: result.integration._id,
          teamName: result.integration.teamName,
          teamId: result.integration.teamId,
          connectedAt: result.integration.connectedAt,
          isUpdate: result.isUpdate,
        },
        message: result.isUpdate
          ? "Slack integration updated successfully"
          : "Slack integration connected successfully",
      });
    } catch (error) {
      console.error("[SLACK-CONNECT] Unexpected error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to connect Slack integration",
        details: { type: "internal_error" },
      });
    }
  });

  /**
   * POST /api/integrations/slack/test-connection
   * Test Slack connection without saving integration
   *
   * Expected payload: { botToken: "xoxb-..." }
   * Success response: { success: true, data: { teamName, teamId, botUserId, botName }, message: "..." }
   * Error response: { success: false, error: "...", details?: {...} }
   */
  router.post(
    "/test-connection",
    verifyToken,
    verifyAdmin(db),
    async (req, res) => {
      console.log(
        `[SLACK-TEST] Request from user ${req.user?.email}, payload:`,
        sanitizeForLogging(req.body)
      );

      try {
        const { botToken } = req.body;

        // Validate required fields
        if (!botToken || typeof botToken !== "string") {
          console.log(
            `[SLACK-TEST] Validation failed: missing or invalid botToken`
          );
          return res.status(400).json({
            success: false,
            error: "Bot token is required for testing",
            details: { field: "botToken", received: typeof botToken },
          });
        }

        // Validate bot token format
        if (!botToken.startsWith("xoxb-")) {
          console.log(`[SLACK-TEST] Validation failed: invalid token format`);
          return res.status(400).json({
            success: false,
            error:
              'Invalid bot token format. Bot tokens should start with "xoxb-"',
            details: { field: "botToken", format: "Expected format: xoxb-..." },
          });
        }

        // Encrypt token for testing
        const { getEncryptionUtil } = require("../utils/encryption");
        const encryptionUtil = getEncryptionUtil();
        const encryptedToken = encryptionUtil.encrypt(botToken);

        console.log(`[SLACK-TEST] Testing connection with encrypted token`);
        const result = await slackService.testSlackConnection(encryptedToken);

        if (!result.success) {
          console.log(`[SLACK-TEST] Connection test failed:`, result.error);
          return res.status(400).json({
            success: false,
            error: result.error,
            details: result.details || { type: "slack_api_error" },
          });
        }

        console.log(
          `[SLACK-TEST] Connection test successful for team: ${result.teamName}`
        );

        res.json({
          success: true,
          data: {
            teamName: result.teamName,
            teamId: result.teamId,
            botUserId: result.botUserId,
            botName: result.botName,
          },
          message: "Slack connection test successful",
        });
      } catch (error) {
        console.error("[SLACK-TEST] Unexpected error:", error);
        res.status(500).json({
          success: false,
          error: "Failed to test Slack connection",
          details: { type: "internal_error" },
        });
      }
    }
  );

  /**
   * PATCH /api/integrations/slack/toggle
   * Enable or disable Slack integration
   *
   * Expected payload: { enabled: boolean }
   * Success response: { success: true, data: { integrationId, enabled, teamName, updatedAt }, message: "..." }
   * Error response: { success: false, error: "...", details?: {...} }
   */
  router.patch("/toggle", verifyToken, verifyAdmin(db), async (req, res) => {
    console.log(
      `[SLACK-TOGGLE] Request from user ${req.user?.email}, payload:`,
      req.body
    );

    try {
      const { enabled } = req.body;

      // Validate required fields
      if (typeof enabled !== "boolean") {
        console.log(
          `[SLACK-TOGGLE] Validation failed: enabled must be boolean, received:`,
          typeof enabled
        );
        return res.status(400).json({
          success: false,
          error: "Enabled field is required and must be a boolean",
          details: {
            field: "enabled",
            received: typeof enabled,
            expected: "boolean",
          },
        });
      }

      const integrationCollection = db.collection("slack_integrations");

      // Find the most recent integration (regardless of active status)
      console.log(`[SLACK-TOGGLE] Looking for most recent integration`);
      const integration = await integrationCollection.findOne(
        {},
        { sort: { updatedAt: -1 } }
      );

      if (!integration) {
        console.log(`[SLACK-TOGGLE] No Slack integration found`);
        return res.status(404).json({
          success: false,
          error: "No Slack integration found. Please connect to Slack first.",
          details: { type: "no_integration" },
        });
      }

      console.log(
        `[SLACK-TOGGLE] Found integration ${integration._id}, current isActive: ${integration.isActive}, setting to: ${enabled}`
      );

      // Update the integration status (keeping the existing logic but with better logging)
      const result = await integrationCollection.updateOne(
        { _id: integration._id },
        {
          $set: {
            isActive: enabled,
            connectionStatus: enabled ? "connected" : "disabled",
            updatedAt: new Date(),
            updatedBy: req.user.userId,
            ...(enabled ? {} : { disabledAt: new Date() }),
            ...(enabled ? { enabledAt: new Date() } : {}),
          },
        }
      );

      if (result.modifiedCount === 0) {
        console.log(`[SLACK-TOGGLE] Update failed: no documents modified`);
        return res.status(500).json({
          success: false,
          error: "Failed to update integration status",
          details: { type: "update_failed" },
        });
      }

      // Get updated integration for response
      const updatedIntegration = await integrationCollection.findOne({
        _id: integration._id,
      });

      console.log(
        `[SLACK-TOGGLE] Successfully ${
          enabled ? "enabled" : "disabled"
        } integration ${integration._id}`
      );

      res.json({
        success: true,
        data: {
          integrationId: updatedIntegration._id,
          enabled: updatedIntegration.isActive,
          teamName: updatedIntegration.teamName,
          updatedAt: updatedIntegration.updatedAt,
        },
        message: `Slack integration ${
          enabled ? "enabled" : "disabled"
        } successfully`,
      });
    } catch (error) {
      console.error("[SLACK-TOGGLE] Unexpected error:", error);
      res.status(500).json({
        success: false,
        error: "Failed to toggle Slack integration",
        details: { type: "internal_error" },
      });
    }
  });

  /**
   * DELETE /api/integrations/slack/disconnect
   * Disconnect Slack integration
   */
  router.delete(
    "/disconnect",
    verifyToken,
    verifyAdmin(db),
    async (req, res) => {
      try {
        const integrationCollection = db.collection("slack_integrations");

        const result = await integrationCollection.updateMany(
          { isActive: true },
          {
            $set: {
              isActive: false,
              connectionStatus: "disconnected",
              disconnectedAt: new Date(),
              updatedAt: new Date(),
            },
          }
        );

        if (result.modifiedCount === 0) {
          return res.status(404).json({
            success: false,
            error: "No active Slack integration found",
          });
        }

        res.json({
          success: true,
          message: "Slack integration disconnected successfully",
        });
      } catch (error) {
        console.error("Error disconnecting Slack integration:", error);
        res.status(500).json({
          success: false,
          error: "Failed to disconnect Slack integration",
        });
      }
    }
  );

  /**
   * GET /api/integrations/slack/mappings
   * Get all channel mappings
   */
  router.get("/mappings", verifyToken, verifyAdmin(db), async (req, res) => {
    try {
      const mappings = await slackService.getChannelMappings();

      res.json({
        success: true,
        data: mappings.map((mapping) => ({
          _id: mapping._id,
          reportType: mapping.reportType,
          slackChannelId: mapping.slackChannelId,
          slackChannelName: mapping.slackChannelName,
          description: mapping.description,
          isActive: mapping.isActive,
          createdAt: mapping.createdAt,
          updatedAt: mapping.updatedAt,
        })),
      });
    } catch (error) {
      console.error("Error getting channel mappings:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get channel mappings",
      });
    }
  });

  /**
   * POST /api/integrations/slack/mappings
   * Create or update channel mapping with enhanced validation
   */
  router.post("/mappings", verifyToken, verifyAdmin(db), async (req, res) => {
    try {
      const { reportType, slackChannelId, slackChannelName, description } =
        req.body;

      // Enhanced payload validation
      const validationErrors = {};

      if (!reportType || typeof reportType !== "string" || !reportType.trim()) {
        validationErrors.reportType =
          "Report type is required and must be a non-empty string";
      }

      if (
        !slackChannelId ||
        typeof slackChannelId !== "string" ||
        !slackChannelId.trim()
      ) {
        validationErrors.slackChannelId =
          "Slack channel ID is required and must be a non-empty string";
      }

      // Validate channel ID format
      if (
        slackChannelId &&
        !/^[CGDW][A-Z0-9]{8,}$/.test(slackChannelId.trim())
      ) {
        validationErrors.slackChannelId =
          "Invalid Slack channel ID format. Must start with C (channel), G (group), D (DM), or W (workspace)";
      }

      // Validate report type format
      if (reportType && !/^[a-zA-Z0-9_]+$/.test(reportType.trim())) {
        validationErrors.reportType =
          "Report type must contain only alphanumeric characters and underscores";
      }

      if (Object.keys(validationErrors).length > 0) {
        return res.status(400).json({
          success: false,
          error: "Validation failed",
          details: validationErrors,
        });
      }

      const mappingData = {
        reportType: reportType.trim(),
        slackChannelId: slackChannelId.trim(),
        slackChannelName: slackChannelName
          ? slackChannelName.trim()
          : undefined,
        description: description ? description.trim() : "",
        isActive: true,
        createdByAdminId: req.user.id, // Set from authenticated user
      };

      const result = await slackService.createOrUpdateChannelMapping(
        mappingData
      );

      if (!result.success) {
        // Return structured error response with user guidance
        const statusCode =
          result.code === "bot_not_member" ||
          result.code === "private_channel_access_denied" ||
          result.code === "channel_not_found"
            ? 400
            : 500;

        return res.status(statusCode).json({
          success: false,
          error: result.error,
          code: result.code,
          userMessage: result.userMessage,
          guidance: result.guidance,
          details: result.details,
        });
      }

      res.json({
        success: true,
        data: {
          _id: result.mapping._id,
          reportType: result.mapping.reportType,
          slackChannelId: result.mapping.slackChannelId,
          slackChannelName: result.mapping.slackChannelName,
          description: result.mapping.description,
          isActive: result.mapping.isActive,
          createdAt: result.mapping.createdAt,
          updatedAt: result.mapping.updatedAt,
        },
        message: result.isUpdate
          ? "Channel mapping updated successfully"
          : "Channel mapping created successfully",
      });
    } catch (error) {
      console.error("Error creating/updating channel mapping:", error);
      res.status(500).json({
        success: false,
        error: "Failed to create/update channel mapping",
        userMessage:
          "An unexpected error occurred while saving the channel mapping. Please try again.",
      });
    }
  });

  /**
   * POST /api/integrations/slack/validate-channel
   * Validate channel access before saving mapping
   */
  router.post(
    "/validate-channel",
    verifyToken,
    verifyAdmin(db),
    async (req, res) => {
      try {
        const { slackChannelId } = req.body;

        if (
          !slackChannelId ||
          typeof slackChannelId !== "string" ||
          !slackChannelId.trim()
        ) {
          return res.status(400).json({
            success: false,
            error: "Slack channel ID is required",
            details: {
              slackChannelId:
                "Channel ID is required and must be a non-empty string",
            },
          });
        }

        // Validate channel ID format
        if (!/^[CGDW][A-Z0-9]{8,}$/.test(slackChannelId.trim())) {
          return res.status(400).json({
            success: false,
            error: "Invalid channel ID format",
            userMessage:
              "Invalid Slack channel ID format. Must start with C (channel), G (group), D (DM), or W (workspace)",
            details: { slackChannelId: "Invalid format" },
          });
        }

        const result = await slackService.validateChannelAccess(
          slackChannelId.trim()
        );

        if (!result.success) {
          return res.status(400).json({
            success: false,
            error: result.error,
            code: result.code,
            userMessage: result.userMessage,
            guidance: result.guidance,
          });
        }

        res.json({
          success: true,
          data: {
            channelId: result.channel.id,
            channelName: result.channel.name,
            isPrivate: result.channel.isPrivate,
            isMember: result.channel.isMember,
            isArchived: result.channel.isArchived,
          },
          message: result.message,
        });
      } catch (error) {
        console.error("Error validating channel:", error);
        res.status(500).json({
          success: false,
          error: "Failed to validate channel",
          userMessage:
            "An unexpected error occurred while validating the channel. Please try again.",
        });
      }
    }
  );

  /**
   * DELETE /api/integrations/slack/mappings/:id
   * Delete channel mapping
   */
  router.delete(
    "/mappings/:id",
    verifyToken,
    verifyAdmin(db),
    async (req, res) => {
      try {
        const { id } = req.params;

        if (!ObjectId.isValid(id)) {
          return res.status(400).json({
            success: false,
            error: "Invalid mapping ID",
          });
        }

        const channelMappingCollection = db.collection(
          "slack_channel_mappings"
        );

        const result = await channelMappingCollection.updateOne(
          { _id: new ObjectId(id) },
          {
            $set: {
              isActive: false,
              updatedAt: new Date(),
            },
          }
        );

        if (result.matchedCount === 0) {
          return res.status(404).json({
            success: false,
            error: "Channel mapping not found",
          });
        }

        res.json({
          success: true,
          message: "Channel mapping deleted successfully",
        });
      } catch (error) {
        console.error("Error deleting channel mapping:", error);
        res.status(500).json({
          success: false,
          error: "Failed to delete channel mapping",
        });
      }
    }
  );

  /**
   * POST /api/integrations/slack/test-mapping
   * Test a channel mapping by sending a test message
   */
  router.post(
    "/test-mapping",
    verifyToken,
    verifyAdmin(db),
    async (req, res) => {
      try {
        const { channelId, reportType } = req.body;

        if (!channelId || !reportType) {
          return res.status(400).json({
            success: false,
            error: "Channel ID and report type are required",
          });
        }

        const result = await slackService.testChannelMapping(
          channelId,
          reportType
        );

        if (!result.success) {
          return res.status(400).json({
            success: false,
            error: result.error,
          });
        }

        res.json({
          success: true,
          message: "Test message sent successfully",
          data: {
            timestamp: result.timestamp,
          },
        });
      } catch (error) {
        console.error("Error testing channel mapping:", error);
        res.status(500).json({
          success: false,
          error: "Failed to test channel mapping",
        });
      }
    }
  );

  return router;
}

module.exports = createSlackIntegrationRoutes;
