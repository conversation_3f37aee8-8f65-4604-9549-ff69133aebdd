# Slack Integration API Documentation

## Overview

The Slack Integration API provides endpoints for connecting, testing, and managing Slack workspace integrations. All endpoints require authentication (JWT token) and admin privileges.

## Base URL
```
http://localhost:500/api/integrations/slack
```

## Authentication
All endpoints require:
- `Authorization: Bearer <JWT_TOKEN>` header
- User must have `admin` or `superadmin` role

## Response Format

### Success Response
```json
{
  "success": true,
  "data": { /* endpoint-specific data */ },
  "message": "Human-readable success message"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Human-readable error message",
  "details": { /* optional additional error context */ }
}
```

## Endpoints

### 1. Connect Integration
**POST** `/connect`

Establishes or updates a Slack integration with the provided bot token.

#### Request Body
```json
{
  "botToken": "xoxb-your-slack-bot-token"
}
```

#### Success Response (200)
```json
{
  "success": true,
  "data": {
    "integrationId": "64f...",
    "teamName": "Your Workspace",
    "teamId": "T1234567890",
    "connectedAt": "2024-01-01T00:00:00.000Z",
    "isUpdate": false
  },
  "message": "Slack integration connected successfully"
}
```

#### Error Responses
- **400**: Invalid or missing bot token
- **401**: Authentication required
- **403**: Admin access required
- **500**: Internal server error

---

### 2. Test Connection
**POST** `/test-connection`

Tests a Slack bot token without saving the integration.

#### Request Body
```json
{
  "botToken": "xoxb-your-slack-bot-token"
}
```

#### Success Response (200)
```json
{
  "success": true,
  "data": {
    "teamName": "Your Workspace",
    "teamId": "T1234567890",
    "botUserId": "U1234567890",
    "botName": "Your Bot"
  },
  "message": "Slack connection test successful"
}
```

#### Error Responses
- **400**: Invalid token or Slack API error
- **401**: Authentication required
- **403**: Admin access required
- **500**: Internal server error

---

### 3. Toggle Integration
**PATCH** `/toggle`

Enables or disables an existing Slack integration.

#### Request Body
```json
{
  "enabled": true
}
```

#### Success Response (200)
```json
{
  "success": true,
  "data": {
    "integrationId": "64f...",
    "enabled": true,
    "teamName": "Your Workspace",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Slack integration enabled successfully"
}
```

#### Error Responses
- **400**: Invalid enabled value (must be boolean)
- **404**: No active integration found
- **401**: Authentication required
- **403**: Admin access required
- **500**: Internal server error

---

## Manual Testing

### Using curl

1. **Get JWT Token**
```bash
curl -X POST http://localhost:500/jwt \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

2. **Test Connection**
```bash
curl -X POST http://localhost:500/api/integrations/slack/test-connection \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"botToken":"xoxb-your-token"}'
```

3. **Connect Integration**
```bash
curl -X POST http://localhost:500/api/integrations/slack/connect \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"botToken":"xoxb-your-token"}'
```

4. **Toggle Integration**
```bash
curl -X PATCH http://localhost:500/api/integrations/slack/toggle \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"enabled":true}'
```

## Troubleshooting

### Common Issues

1. **404 Not Found**
   - Verify the route path and HTTP method
   - Check that the server is running
   - Ensure routes are properly mounted

2. **400 Bad Request**
   - Check request payload format
   - Verify required fields are present
   - Validate bot token format (must start with "xoxb-")

3. **401/403 Authentication Errors**
   - Verify JWT token is valid and not expired
   - Ensure user has admin privileges
   - Check Authorization header format

4. **500 Internal Server Error**
   - Check server logs for detailed error information
   - Verify database connectivity
   - Check Slack API connectivity

### Debugging Tips

- Server logs include detailed request/response information with `[SLACK-*]` prefixes
- Bot tokens are sanitized in logs (only first 10 characters shown)
- All validation errors include `details` field with specific information
- Check MongoDB collections: `slack_integrations`, `users`

## Frontend Integration

The frontend should use these exact payload formats:

```javascript
// Test connection
const testResult = await axiosSecure.post('/api/integrations/slack/test-connection', {
  botToken: 'xoxb-...'
});

// Connect integration
const connectResult = await axiosSecure.post('/api/integrations/slack/connect', {
  botToken: 'xoxb-...'
});

// Toggle integration
const toggleResult = await axiosSecure.patch('/api/integrations/slack/toggle', {
  enabled: true
});
```
