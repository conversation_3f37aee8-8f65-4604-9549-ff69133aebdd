import React, { useState } from "react";
import { FaCog, FaEllipsisH } from "react-icons/fa";

/**
 * Modern integration card component matching the screenshot design
 * Features: Enable/disable toggle, settings gear icon, clean layout
 */
const ModernIntegrationCard = ({
  icon: Icon,
  name,
  description,
  isEnabled = false,
  onToggle,
  onSettings,
  onMenuClick,
  className = "",
}) => {
  const [isToggling, setIsToggling] = useState(false);

  const handleToggle = async () => {
    if (isToggling) return;

    setIsToggling(true);
    try {
      await onToggle?.(!isEnabled);
    } finally {
      setIsToggling(false);
    }
  };

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200 ${className}`}
    >
      {/* Header with icon and menu */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-2">
          {Icon && (
            <div className="w-10 h-10 flex items-center justify-center">
              <Icon className="w-8 h-8 text-blue-600" />
            </div>
          )}

          {/* Integration name */}
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {name}
          </h3>
        </div>

        {onMenuClick && (
          <button
            onClick={onMenuClick}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            aria-label="More options"
          >
            <FaEllipsisH className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Description */}
      <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed mb-6">
        {description}
      </p>

      {/* Footer with settings and toggle */}
      <div className="flex items-center justify-between">
        <button
          onClick={onSettings}
          className="flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
        >
          <FaCog className="w-4 h-4" />
          Settings
        </button>

        {/* Toggle Switch */}
        <div className="flex items-center">
          <button
            onClick={handleToggle}
            disabled={isToggling}
            className={`
              relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50
              ${isEnabled ? "bg-blue-600" : "bg-gray-200 dark:bg-gray-600"}
            `}
            role="switch"
            aria-checked={isEnabled}
            aria-label={`${
              isEnabled ? "Disable" : "Enable"
            } ${name} integration`}
          >
            <span
              className={`
                inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200
                ${isEnabled ? "translate-x-6" : "translate-x-1"}
              `}
            />
          </button>

          {isToggling && (
            <div className="ml-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModernIntegrationCard;
