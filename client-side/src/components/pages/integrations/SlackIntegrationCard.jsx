import React, { useState } from "react";
import { FaSlack } from "react-icons/fa";
import IntegrationCard from "./IntegrationCard";
import MappingList from "./MappingList";
import MappingModal from "./MappingModal";
import SlackConnectionModal from "./SlackConnectionModal";
import {
  useSlackIntegrationStatus,
  useSlackChannelMappings,
  useSlackMappingTest,
  useSlackMappingMutations,
  getIntegrationStatusInfo,
} from "../../../hooks/useSlackIntegration";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAxiosSecure } from "../../../hooks/useAxiosSecure";
import { showToast } from "../../utils/toasters/toastService";

/**
 * Specialized Slack integration card component
 * Compact, self-contained card for dashboard integration
 */
const SlackIntegrationCard = ({ onEdit }) => {
  const [showMappingModal, setShowMappingModal] = useState(false);
  const [editingMapping, setEditingMapping] = useState(null);
  const [showConnectionModal, setShowConnectionModal] = useState(false);

  // Hooks
  const { data: statusData, isLoading: statusLoading } =
    useSlackIntegrationStatus();
  const { data: mappingsData, isLoading: mappingsLoading } =
    useSlackChannelMappings();
  const testMappingMutation = useSlackMappingTest();
  const { deleteMutation } = useSlackMappingMutations();

  // Custom save mutation for this component (no toast)
  const axiosSecure = useAxiosSecure();
  const queryClient = useQueryClient();

  const saveMutation = useMutation({
    mutationFn: async (mappingData) => {
      const response = await axiosSecure.post(
        "/api/integrations/slack/mappings",
        mappingData
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["slackChannelMappings"]);
      // No toast here - will be handled by the component
    },
    onError: (error) => {
      console.error("Failed to save mapping:", error);
      // Error handling will be done in the component
    },
  });

  // Data processing
  const statusInfo = getIntegrationStatusInfo(statusData);
  const mappings = mappingsData?.data || [];

  // Report types (should ideally come from API)
  const reportTypes = [
    { value: "daily_work", label: "Daily Work Report" },
    { value: "shopify", label: "Shopify Report" },
    { value: "support", label: "Support Report" },
    { value: "project_update", label: "Project Update" },
    { value: "weekly_summary", label: "Weekly Summary" },
  ];

  // Event handlers
  const handleAddMapping = () => {
    setEditingMapping(null);
    setShowMappingModal(true);
  };

  const handleEditMapping = (mapping) => {
    setEditingMapping(mapping);
    setShowMappingModal(true);
  };

  const handleSaveMapping = (mappingData) => {
    saveMutation.mutate(mappingData, {
      onSuccess: () => {
        // Close modal and reset state on successful save
        setShowMappingModal(false);
        setEditingMapping(null);
        // Show single success toast
        showToast("success", "Channel mapping saved successfully!", {
          icon: "💾",
        });
      },
      onError: (error) => {
        const errorData = error.response?.data;
        let message = errorData?.error || "Failed to save channel mapping";

        // Handle specific error cases with better messaging
        if (errorData?.details) {
          if (errorData.details.advice) {
            message += `\n\n${errorData.details.advice}`;
          }

          // Handle validation errors
          if (
            errorData.details.reportType ||
            errorData.details.slackChannelId
          ) {
            const validationMessages = [];
            if (errorData.details.reportType) {
              validationMessages.push(
                `Report Type: ${errorData.details.reportType}`
              );
            }
            if (errorData.details.slackChannelId) {
              validationMessages.push(
                `Channel ID: ${errorData.details.slackChannelId}`
              );
            }
            message = `Validation failed:\n${validationMessages.join("\n")}`;
          }
        }

        showToast("error", message, {
          icon: "❌",
          duration: errorData?.details?.advice ? 8000 : 4000,
        });
      },
    });
  };

  const handleDeleteMapping = (mappingId) => {
    deleteMutation.mutate(mappingId);
  };

  const handleTestMapping = (mapping) => {
    testMappingMutation.mutate({
      channelId: mapping.slackChannelId,
      reportType: mapping.reportType,
    });
  };

  const handleQuickTest = () => {
    if (mappings.length > 0) {
      // Test the first mapping as a quick test
      handleTestMapping(mappings[0]);
    }
  };

  const handleEditConnection = () => {
    if (onEdit) {
      onEdit();
    } else {
      setShowConnectionModal(true);
    }
  };

  // Footer content
  const footer = (
    <div className="flex justify-between items-center">
      <div className="text-xs text-gray-400 dark:text-gray-500">
        {statusInfo.lastSync
          ? `Last synced: ${statusInfo.lastSync}`
          : "Never synced"}
      </div>
      {mappings.length > 0 && (
        <button
          onClick={handleQuickTest}
          disabled={testMappingMutation.isPending}
          className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:opacity-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
        >
          {testMappingMutation.isPending ? (
            <span className="loading loading-spinner loading-xs"></span>
          ) : (
            "Send Test"
          )}
        </button>
      )}
    </div>
  );

  if (statusLoading) {
    return (
      <IntegrationCard
        integrationName="Slack Integration"
        icon={FaSlack}
        status="pending"
        subtitle="Loading..."
      >
        <div className="space-y-3">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
      </IntegrationCard>
    );
  }

  return (
    <>
      <IntegrationCard
        integrationName="Slack Integration"
        icon={FaSlack}
        status={statusInfo.status}
        subtitle={statusInfo.subtitle}
        onEdit={handleEditConnection}
        onTest={mappings.length > 0 ? handleQuickTest : undefined}
        footer={footer}
      >
        {/* Mapping List */}
        <MappingList
          mappings={mappings}
          reportTypes={reportTypes}
          onAddMapping={handleAddMapping}
          onEditMapping={handleEditMapping}
          onDeleteMapping={handleDeleteMapping}
          onTestMapping={handleTestMapping}
          isLoading={mappingsLoading}
          maxVisibleMappings={3}
        />

        {/* Connection Status Details */}
        {statusInfo.status === "error" && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg dark:bg-red-900/20 dark:border-red-800">
            <p className="text-sm text-red-700 dark:text-red-300">
              Connection failed. Please check your bot token and try
              reconnecting.
            </p>
          </div>
        )}

        {statusInfo.status === "disconnected" && mappings.length === 0 && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Connect your Slack workspace to start posting reports
              automatically.
            </p>
          </div>
        )}
      </IntegrationCard>

      {/* Mapping Modal */}
      <MappingModal
        isOpen={showMappingModal}
        onClose={() => {
          setShowMappingModal(false);
          setEditingMapping(null);
        }}
        onSave={handleSaveMapping}
        editingMapping={editingMapping}
        isLoading={saveMutation.isPending}
        reportTypes={reportTypes}
      />

      {/* Connection Modal */}
      <SlackConnectionModal
        isOpen={showConnectionModal}
        onClose={() => setShowConnectionModal(false)}
        currentStatus={statusData?.data}
      />
    </>
  );
};

export default SlackIntegrationCard;
