import React, { useState } from "react";
import { useForm } from "react-hook-form";
import {
  FaTimes,
  FaSlack,
  FaEye,
  FaEyeSlash,
  FaCheckCircle,
  FaExclamationTriangle,
  FaCog,
  FaPlus,
  FaTrash,
} from "react-icons/fa";
import { MdSave, MdSettings } from "react-icons/md";
import ConnectionTestPanel from "./ConnectionTestPanel";
import ConfirmationModal from "../../utils/modals/ConfirmationModal";
import {
  useSlackConnect,
  useSlackDisconnect,
  useSlackMappingMutations,
  useSlackChannelMappings,
  useReportTypeMutations,
} from "../../../hooks/useSlackIntegration";
import { showToast } from "../../utils/toasters/toastService";

/**
 * Consolidated Slack integration modal
 * Combines connection setup, status display, and channel mapping configuration
 */
const SlackConnectionModal = ({ isOpen, onClose, currentStatus = null }) => {
  const [showToken, setShowToken] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [activeTab, setActiveTab] = useState("connection"); // 'connection', 'channels', or 'reports'
  const [showDisconnectConfirm, setShowDisconnectConfirm] = useState(false);
  const [channelMappings, setChannelMappings] = useState([
    {
      id: 1,
      channel: "#daily-reports",
      channelId: "C1234567890",
      reportType: "daily-work",
      enabled: true,
    },
    {
      id: 2,
      channel: "#shopify-updates",
      channelId: "C0987654321",
      reportType: "shopify",
      enabled: true,
    },
  ]);
  const [reportTypes, setReportTypes] = useState([
    { id: 1, name: "daily-work", label: "Daily Work Reports", enabled: true },
    { id: 2, name: "shopify", label: "Shopify Reports", enabled: true },
    {
      id: 3,
      name: "meeting-summary",
      label: "Meeting Summaries",
      enabled: true,
    },
    { id: 4, name: "task-updates", label: "Task Updates", enabled: true },
  ]);

  const connectMutation = useSlackConnect();
  const disconnectMutation = useSlackDisconnect();
  const { saveMutation: saveMappingMutation } = useSlackMappingMutations();
  const { data: existingMappings } = useSlackChannelMappings();
  const {
    createMutation: createReportTypeMutation,
    updateMutation: updateReportTypeMutation,
  } = useReportTypeMutations();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
  } = useForm();

  const botToken = watch("botToken");
  const isConnected = currentStatus?.connected;

  const handleFormSubmit = (data) => {
    connectMutation.mutate(data, {
      onSuccess: () => {
        reset();
        // Don't close modal - let user see the connected state
      },
    });
  };

  const handleDisconnect = () => {
    setShowDisconnectConfirm(true);
  };

  const handleConfirmDisconnect = () => {
    disconnectMutation.mutate(undefined, {
      onSuccess: () => {
        setShowDisconnectConfirm(false);
        // Don't close modal - let user see the disconnected state
      },
      onError: () => {
        setShowDisconnectConfirm(false);
      },
    });
  };

  const handleCancelDisconnect = () => {
    setShowDisconnectConfirm(false);
  };

  const handleClose = () => {
    reset();
    setTestResult(null);
    setShowDisconnectConfirm(false);
    onClose();
  };

  const handleTestSuccess = (result) => {
    setTestResult({ success: true, data: result.data });
    // Don't close modal - let user see the connected state
  };

  const handleTestError = (error) => {
    setTestResult({ success: false, error });
  };

  // Channel mapping functions
  const addChannelMapping = () => {
    const newMapping = {
      id: Date.now(),
      channel: "",
      channelId: "",
      reportType: "daily-work",
      enabled: true,
    };
    setChannelMappings([...channelMappings, newMapping]);
  };

  const removeChannelMapping = (id) => {
    setChannelMappings(channelMappings.filter((mapping) => mapping.id !== id));
  };

  const updateChannelMapping = (id, field, value) => {
    setChannelMappings(
      channelMappings.map((mapping) =>
        mapping.id === id ? { ...mapping, [field]: value } : mapping
      )
    );
  };

  const saveChannelMappings = async () => {
    try {
      // Save each channel mapping
      for (const mapping of channelMappings) {
        if (mapping.channel && mapping.channelId && mapping.reportType) {
          await saveMappingMutation.mutateAsync({
            reportType: mapping.reportType,
            slackChannelId: mapping.channelId,
            description: `Channel: ${mapping.channel}`,
          });
        }
      }

      showToast("success", "Channel mappings saved successfully!", {
        icon: "💾",
      });
    } catch (error) {
      showToast("error", "Failed to save channel mappings", { icon: "❌" });
    }
  };

  // Report type functions
  const addReportType = () => {
    const newReportType = {
      id: Date.now(),
      name: "",
      label: "",
      enabled: true,
    };
    setReportTypes([...reportTypes, newReportType]);
  };

  const removeReportType = (id) => {
    setReportTypes(reportTypes.filter((type) => type.id !== id));
  };

  const updateReportType = (id, field, value) => {
    setReportTypes(
      reportTypes.map((type) =>
        type.id === id ? { ...type, [field]: value } : type
      )
    );
  };

  const saveReportTypes = async () => {
    try {
      // Save each report type
      for (const reportType of reportTypes) {
        if (reportType.name && reportType.label) {
          // Generate a key from the name if not provided
          const key = reportType.name
            .toLowerCase()
            .replace(/\s+/g, "_")
            .replace(/[^a-z0-9_]/g, "");

          await createReportTypeMutation.mutateAsync({
            key: key,
            name: reportType.label || reportType.name,
            description: `Custom report type: ${
              reportType.label || reportType.name
            }`,
            isActive: reportType.enabled,
            sortOrder: reportType.id || 0,
          });
        }
      }

      showToast("success", "Report types saved successfully!", { icon: "💾" });
    } catch (error) {
      showToast("error", "Failed to save report types", { icon: "❌" });
    }
  };

  const getStatusIcon = () => {
    if (isConnected) {
      return <FaCheckCircle className="w-5 h-5 text-green-500" />;
    }
    return <FaExclamationTriangle className="w-5 h-5 text-red-500" />;
  };

  const getStatusText = () => {
    return isConnected ? "Connected" : "Disconnected";
  };

  const getStatusColor = () => {
    return isConnected
      ? "px-2 py-1 bg-green-100 text-green-600 dark:text-green-400 rounded-full"
      : "px-2 py-1 bg-red-100 text-red-600 dark:text-red-400 rounded-full";
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity"
        onClick={handleClose}
      />

      {/* Modal */}
      <div className="fixed inset-0 z-50 overflow-hidden">
        <div className="flex min-h-full items-center justify-center p-4">
          <div className="relative w-full max-w-2xl bg-white dark:bg-gray-800 rounded-2xl shadow-xl">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <FaSlack className="w-8 h-8 text-blue-600" />
                <div className="flex items-center gap-4">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Slack Integration
                  </h2>
                  <div className="flex items-center gap-2">
                    {/* {getStatusIcon()} */}
                    <span className={`text-sm font-medium ${getStatusColor()}`}>
                      {getStatusText()}
                    </span>
                    {currentStatus?.teamName && (
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        • {currentStatus.teamName}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <FaTimes className="w-5 h-5" />
              </button>
            </div>

            {/* Tabs */}
            <div className="flex border-b border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setActiveTab("connection")}
                className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
                  activeTab === "connection"
                    ? "text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20"
                    : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <FaCog className="w-4 h-4" />
                  Connection Settings
                </div>
              </button>
              <button
                onClick={() => setActiveTab("channels")}
                className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
                  activeTab === "channels"
                    ? "text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20"
                    : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <MdSettings className="w-4 h-4" />
                  Channel Mapping
                </div>
              </button>
              <button
                onClick={() => setActiveTab("reports")}
                className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
                  activeTab === "reports"
                    ? "text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20"
                    : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <FaPlus className="w-4 h-4" />
                  Report Types
                </div>
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              {activeTab === "connection" && (
                <div className="space-y-6">
                  {/* Current Status */}
                  {isConnected && currentStatus && (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg dark:bg-green-900/20 dark:border-green-800">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium text-green-800 dark:text-green-200">
                            Currently Connected
                          </div>
                          {currentStatus.teamName && (
                            <div className="text-sm text-green-700 dark:text-green-300">
                              Workspace: {currentStatus.teamName}
                            </div>
                          )}
                          {currentStatus.connectedAt && (
                            <div className="text-xs text-green-600 dark:text-green-400 mt-1">
                              Connected at{" "}
                              {new Date(
                                currentStatus.connectedAt
                              ).toLocaleString()}
                            </div>
                          )}
                        </div>
                        <button
                          onClick={handleDisconnect}
                          disabled={
                            disconnectMutation.isPending ||
                            showDisconnectConfirm
                          }
                          className="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 transition-colors"
                          aria-label="Disconnect Slack integration"
                        >
                          {disconnectMutation.isPending
                            ? "Disconnecting..."
                            : "Disconnect"}
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Form - Only show when not connected */}
                  {!isConnected && (
                    <>
                      <form
                        onSubmit={handleSubmit(handleFormSubmit)}
                        className="space-y-4"
                      >
                        {/* Bot Token Input */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Bot Token *
                          </label>
                          <div className="relative">
                            <input
                              type={showToken ? "text" : "password"}
                              {...register("botToken", {
                                required: "Bot token is required",
                                pattern: {
                                  value: /^xoxb-/,
                                  message: "Bot token must start with 'xoxb-'",
                                },
                              })}
                              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                              placeholder="xoxb-your-bot-token-here"
                            />
                            <button
                              type="button"
                              onClick={() => setShowToken(!showToken)}
                              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                            >
                              {showToken ? <FaEyeSlash /> : <FaEye />}
                            </button>
                          </div>
                          {errors.botToken && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                              {errors.botToken.message}
                            </p>
                          )}
                          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Get your bot token from your Slack app settings
                          </p>
                        </div>

                        {/* Connection Test */}
                        <ConnectionTestPanel
                          botToken={botToken}
                          onTestSuccess={handleTestSuccess}
                          onTestError={handleTestError}
                        />
                      </form>

                      {/* Help Text - Only show when not connected */}
                      <div className="text-xs text-gray-500 dark:text-gray-400 space-y-2">
                        <p>
                          <strong>How to get your bot token:</strong>
                        </p>
                        <ol className="list-decimal list-inside space-y-1 ml-2">
                          <li>
                            Go to{" "}
                            <a
                              href="https://api.slack.com/apps"
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:underline"
                            >
                              api.slack.com/apps
                            </a>
                          </li>
                          <li>Select your app or create a new one</li>
                          <li>Go to "OAuth & Permissions"</li>
                          <li>Copy the "Bot User OAuth Token"</li>
                        </ol>
                      </div>
                    </>
                  )}

                  {/* Close Button - Always show */}
                  <div className="flex justify-end pt-4">
                    <button
                      type="button"
                      onClick={handleClose}
                      className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                    >
                      Close
                    </button>
                  </div>
                </div>
              )}

              {activeTab === "channels" && (
                <div className="space-y-6">
                  {/* Channel Mapping Header */}
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        Channel Mapping
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Configure which Slack channels receive notifications for
                        reports
                      </p>
                    </div>
                    <button
                      onClick={addChannelMapping}
                      className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <FaPlus className="w-4 h-4" />
                      Add Mapping
                    </button>
                  </div>

                  {/* Channel Mappings List */}
                  <div className="space-y-3">
                    {channelMappings.map((mapping) => (
                      <div
                        key={mapping.id}
                        className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                      >
                        {/* Channel Input */}
                        <div className="flex-1">
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Slack Channel
                          </label>
                          <input
                            type="text"
                            value={mapping.channel}
                            onChange={(e) =>
                              updateChannelMapping(
                                mapping.id,
                                "channel",
                                e.target.value
                              )
                            }
                            placeholder="#channel-name"
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                          />
                        </div>

                        {/* Channel ID Input */}
                        <div className="flex-1">
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Slack Channel ID
                          </label>
                          <input
                            type="text"
                            value={mapping.channelId}
                            onChange={(e) =>
                              updateChannelMapping(
                                mapping.id,
                                "channelId",
                                e.target.value
                              )
                            }
                            placeholder="C1234567890"
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                          />
                        </div>

                        {/* Report Type Select */}
                        <div className="flex-1">
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Report Type
                          </label>
                          <select
                            value={mapping.reportType}
                            onChange={(e) =>
                              updateChannelMapping(
                                mapping.id,
                                "reportType",
                                e.target.value
                              )
                            }
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                          >
                            {reportTypes
                              .filter((type) => type.enabled)
                              .map((type) => (
                                <option key={type.id} value={type.name}>
                                  {type.label}
                                </option>
                              ))}
                          </select>
                        </div>

                        {/* Enable Toggle */}
                        <div className="flex items-center gap-2">
                          <label className="text-xs font-medium text-gray-700 dark:text-gray-300">
                            Enabled
                          </label>
                          <button
                            onClick={() =>
                              updateChannelMapping(
                                mapping.id,
                                "enabled",
                                !mapping.enabled
                              )
                            }
                            className={`
                              relative inline-flex h-5 w-9 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                              ${
                                mapping.enabled
                                  ? "bg-blue-600"
                                  : "bg-gray-200 dark:bg-gray-600"
                              }
                            `}
                          >
                            <span
                              className={`
                                inline-block h-3 w-3 transform rounded-full bg-white transition-transform duration-200
                                ${
                                  mapping.enabled
                                    ? "translate-x-5"
                                    : "translate-x-1"
                                }
                              `}
                            />
                          </button>
                        </div>

                        {/* Remove Button */}
                        <button
                          onClick={() => removeChannelMapping(mapping.id)}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors"
                        >
                          <FaTrash className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>

                  {/* Save Button */}
                  <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
                    <button
                      onClick={saveChannelMappings}
                      className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <MdSave className="w-4 h-4" />
                      Save Channel Mappings
                    </button>
                  </div>
                </div>
              )}

              {activeTab === "reports" && (
                <div className="space-y-6">
                  {/* Report Types Header */}
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        Report Types
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Manage available report types for channel mapping
                      </p>
                    </div>
                    <button
                      onClick={addReportType}
                      className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <FaPlus className="w-4 h-4" />
                      Add Report Type
                    </button>
                  </div>

                  {/* Report Types List */}
                  <div className="space-y-3">
                    {reportTypes.map((reportType) => (
                      <div
                        key={reportType.id}
                        className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                      >
                        {/* Name Input */}
                        <div className="flex-1">
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Name (ID)
                          </label>
                          <input
                            type="text"
                            value={reportType.name}
                            onChange={(e) =>
                              updateReportType(
                                reportType.id,
                                "name",
                                e.target.value
                              )
                            }
                            placeholder="daily-work"
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                          />
                        </div>

                        {/* Label Input */}
                        <div className="flex-1">
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Display Label
                          </label>
                          <input
                            type="text"
                            value={reportType.label}
                            onChange={(e) =>
                              updateReportType(
                                reportType.id,
                                "label",
                                e.target.value
                              )
                            }
                            placeholder="Daily Work Reports"
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                          />
                        </div>

                        {/* Enable Toggle */}
                        <div className="flex items-center gap-2">
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300">
                            Enabled
                          </label>
                          <button
                            onClick={() =>
                              updateReportType(
                                reportType.id,
                                "enabled",
                                !reportType.enabled
                              )
                            }
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              reportType.enabled
                                ? "bg-blue-600"
                                : "bg-gray-200 dark:bg-gray-600"
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                reportType.enabled
                                  ? "translate-x-6"
                                  : "translate-x-1"
                              }`}
                            />
                          </button>
                        </div>

                        {/* Remove Button */}
                        <button
                          onClick={() => removeReportType(reportType.id)}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors"
                        >
                          <FaTrash className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>

                  {/* Save Button */}
                  <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
                    <button
                      onClick={saveReportTypes}
                      className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <MdSave className="w-4 h-4" />
                      Save Report Types
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Disconnect Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDisconnectConfirm}
        onClose={handleCancelDisconnect}
        onConfirm={handleConfirmDisconnect}
        title="Disconnect Slack?"
        message="Are you sure you want to disconnect Slack? This will disable all Slack notifications and mappings."
        confirmText="Yes, Disconnect"
        cancelText="Cancel"
        confirmButtonClass="bg-red-600 hover:bg-red-700 text-white"
        isLoading={disconnectMutation.isPending}
      />
    </>
  );
};

export default SlackConnectionModal;
