#!/usr/bin/env node

/**
 * Debug script to test SlackIntegrationService directly
 */

const { MongoClient } = require('mongodb');
const SlackIntegrationService = require('./server-side/services/slackIntegrationService');

const MONGODB_URI = 'mongodb://127.0.0.1:27017';
const DB_NAME = 'wpdevDB';
const BOT_TOKEN = '*********************************************************';

async function debugSlackService() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    console.log('🔌 Connecting to MongoDB...');
    await client.connect();
    
    const db = client.db(DB_NAME);
    console.log('✅ Connected to MongoDB');
    
    console.log('🔧 Initializing SlackIntegrationService...');
    const slackService = new SlackIntegrationService(db);
    console.log('✅ SlackIntegrationService initialized');
    
    console.log('🔐 Testing encryption...');
    const { getEncryptionUtil } = require('./server-side/utils/encryption');
    const encryptionUtil = getEncryptionUtil();
    console.log('✅ Encryption utility loaded');
    
    console.log('🔒 Encrypting bot token...');
    const encryptedToken = encryptionUtil.encrypt(BOT_TOKEN);
    console.log('✅ Bot token encrypted');
    
    console.log('🧪 Testing Slack connection...');
    const result = await slackService.testSlackConnection(encryptedToken);
    
    if (result.success) {
      console.log('✅ Slack connection test successful!');
      console.log('📊 Connection details:');
      console.log(`   - Team Name: ${result.teamName}`);
      console.log(`   - Team ID: ${result.teamId}`);
      console.log(`   - Bot Name: ${result.botName}`);
      console.log(`   - Bot User ID: ${result.botUserId}`);
    } else {
      console.log('❌ Slack connection test failed:');
      console.log(`   - Error: ${result.error}`);
      console.log(`   - Details:`, result.details);
    }
    
    return result;
    
  } catch (error) {
    console.error('💥 Debug test failed:', error);
    console.error('Stack trace:', error.stack);
    throw error;
  } finally {
    await client.close();
    console.log('🔌 MongoDB connection closed');
  }
}

// Run the debug test
if (require.main === module) {
  debugSlackService()
    .then((result) => {
      if (result.success) {
        console.log('\n🎉 Debug test completed successfully!');
        process.exit(0);
      } else {
        console.log('\n💥 Debug test failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Debug execution failed:', error);
      process.exit(1);
    });
}
