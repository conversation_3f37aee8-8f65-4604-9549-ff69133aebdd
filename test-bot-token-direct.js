#!/usr/bin/env node

/**
 * Direct test of the Slack bot token using Slack Web API
 */

const { WebClient } = require('@slack/web-api');

const BOT_TOKEN = '*********************************************************';

async function testBotTokenDirect() {
  console.log('🧪 Testing Slack bot token directly...');
  
  try {
    const client = new WebClient(BOT_TOKEN);
    const response = await client.auth.test();
    
    console.log('✅ Bot token is valid!');
    console.log('📊 Connection details:');
    console.log(`   - Team Name: ${response.team}`);
    console.log(`   - Team ID: ${response.team_id}`);
    console.log(`   - Bot Name: ${response.user}`);
    console.log(`   - Bot User ID: ${response.user_id}`);
    console.log(`   - URL: ${response.url}`);
    
    return true;
  } catch (error) {
    console.log('❌ Bot token test failed:');
    console.log(`   - Error: ${error.message}`);
    console.log(`   - Code: ${error.code}`);
    console.log(`   - Data:`, error.data);
    
    return false;
  }
}

// Run the test
if (require.main === module) {
  testBotTokenDirect()
    .then(success => {
      if (success) {
        console.log('\n🎉 Direct bot token test passed!');
        process.exit(0);
      } else {
        console.log('\n💥 Direct bot token test failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}
